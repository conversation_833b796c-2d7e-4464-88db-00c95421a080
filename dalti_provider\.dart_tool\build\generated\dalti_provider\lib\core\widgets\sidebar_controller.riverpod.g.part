// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$sidebarControllerHash() => r'b1bb83eb9a7fbb07b4173ade00cdf60ba9ba36aa';

/// Sidebar controller for managing sidebar state
///
/// Copied from [SidebarController].
@ProviderFor(SidebarController)
final sidebarControllerProvider =
    AutoDisposeNotifierProvider<SidebarController, SidebarState>.internal(
  SidebarController.new,
  name: r'sidebarControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$sidebarControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SidebarController = AutoDisposeNotifier<SidebarState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

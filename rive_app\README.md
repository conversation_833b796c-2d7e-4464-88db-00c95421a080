# Animated App with R<PERSON> and Flutter

An application with interactive UI and animations, with custom UI components like Bottom TabBar and SideBar, and using animated assets from [Rive](https://rive.app).

<p align="center">
  <img alt="Flutter + Rive UI Preview" src="https://user-images.githubusercontent.com/46301285/212767021-ce434bc0-d6f8-41c1-a17a-360ea225009b.png" height="250px">
</p>

<h3 align="center">
  <a href="https://aashu-dubey.github.io/flutter-samples/#/course-rive">
    Web Demo
  </a>
</h3>

## 👀 This is how it looks

https://user-images.githubusercontent.com/46301285/213805689-d5c8eb3f-12d0-42ef-bdae-4bd857113579.mp4

## 📦 Packages used

1. [rive](https://pub.dev/packages/rive): To render and control the [Rive assets](../../../../assets/samples/ui/rive_app/rive).

## 🔤 Fonts used

1. [Inter-Regular.ttf](../../../../assets/fonts/Inter-Regular.ttf)
2. [Inter-SemiBold](../../../../assets/fonts/Inter-SemiBold.ttf)
3. [Poppins-Bold](../../../../assets/fonts/Poppins-Bold.ttf)

## 🎥 [Tutorials](https://youtube.com/playlist?list=PLpnMM6hhRccigVfEO2Ynj6DQB9MbW5CaF)

| Episodes |  |
| - | - |
| <h3>1. [OnBoarding and SignIn UI](https://youtu.be/vmdafWtYzBg)</h3>🔖 Topics Covered<br>• OnBoarding UI<br>• SignIn UI<br>• Using SignIn UI as custom modal with transition animation<br>• Using and controlling Rive assets for animation<br>• Some Rive topics like Artboard, State Machines, one-shot animation etc.<br>• Some flutter Widgets like RepaintBoundary, ImageFilter, AnimatedBuilder etc. | <a href="https://youtu.be/vmdafWtYzBg" title="Flutter & Rive Tutorial - OnBoarding and SignIn UI"><img src="https://i.ytimg.com/vi/vmdafWtYzBg/maxresdefault.jpg" width="320px" alt="Flutter & Rive Tutorial - OnBoarding and SignIn UI Thumbnail" /></a> |
| <h3>2. [Bottom Tab Bar and Home UI](https://youtu.be/a7_CSrT8CYI)</h3>🔖 Topics Covered<br>• Custom Bottom TabBar with animated icons<br>• Home Screen UI<br>• Update screen based on active tab | <a href="https://youtu.be/a7_CSrT8CYI" title="Flutter & Rive Tutorial - Bottom Tab Bar & Home UI"><img src="https://i.ytimg.com/vi/a7_CSrT8CYI/maxresdefault.jpg" width="320px" alt="Flutter & Rive Tutorial - Bottom Tab Bar & Home UI Thumbnail" /></a> |
| <h3>3. [Side Menu and Wrap up](https://youtu.be/LHbzddzD1W4)</h3>🔖 Topics Covered<br>• Custom Side Menu UI with animated icons<br>• Combining Side Menu and other parts of UI with Home<br>• Presenting Side Menu & OnBoarding with animations as well as handling visibility of other UI elements | <a href="https://youtu.be/LHbzddzD1W4" title="Flutter & Rive Tutorial - Side Menu and Wrap up"><img src="https://i.ytimg.com/vi/LHbzddzD1W4/maxresdefault.jpg" width="320px" alt="Flutter & Rive Tutorial - Side Menu and Wrap up Thumbnail" /></a> |

## 🌻 Motivation

This app is a full Flutter replication of [this SwiftUI course](https://designcode.io/swiftui-rive-animated-app) by [@MengTo](https://twitter.com/MengTo).

## 🔗 Links

- [SwiftUI + Rive](https://designcode.io/swiftui-rive-animated-app): Original course in SwiftUI.
- [🎥 Flutter + Rive YouTube Playlist](https://youtube.com/playlist?list=PLpnMM6hhRccigVfEO2Ynj6DQB9MbW5CaF): Video tutorials with step-by-step implementation of the project from scratch.
- [Ionic Rive](https://github.com/Aashu-Dubey/Ionic-UI-Templates/tree/main/ionic_ui_templates/src/app/templates/course-rive): Ionic + Angular version for the same.
- **Demo: [web](https://aashu-dubey.github.io/flutter-samples/#/course-rive) \| [youtube](https://youtube.com/shorts/jek80t32XqY) \| [twitter](https://twitter.com/aashudubey_ad/status/1616536431010406400)**

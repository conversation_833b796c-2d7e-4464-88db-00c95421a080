import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'sidebar_controller.g.dart';

/// Sidebar state
class SidebarState {
  final bool isOpen;
  final bool isAnimating;

  const SidebarState({
    this.isOpen = false,
    this.isAnimating = false,
  });

  SidebarState copyWith({
    bool? isOpen,
    bool? isAnimating,
  }) {
    return SidebarState(
      isOpen: isOpen ?? this.isOpen,
      isAnimating: isAnimating ?? this.isAnimating,
    );
  }
}

/// Sidebar controller for managing sidebar state
@riverpod
class SidebarController extends _$SidebarController {
  @override
  SidebarState build() {
    return const SidebarState();
  }

  /// Open the sidebar
  void open() {
    if (!state.isOpen && !state.isAnimating) {
      state = state.copyWith(isOpen: true, isAnimating: true);
      
      // Reset animating state after animation duration
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          state = state.copyWith(isAnimating: false);
        }
      });
    }
  }

  /// Close the sidebar
  void close() {
    if (state.isOpen && !state.isAnimating) {
      state = state.copyWith(isOpen: false, isAnimating: true);
      
      // Reset animating state after animation duration
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          state = state.copyWith(isAnimating: false);
        }
      });
    }
  }

  /// Toggle the sidebar
  void toggle() {
    if (state.isOpen) {
      close();
    } else {
      open();
    }
  }
}

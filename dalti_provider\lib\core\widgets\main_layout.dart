import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../theme/theme_provider.dart';
import '../../features/dashboard/providers/profile_completion_provider.dart';
import '../../features/messages/providers/message_provider.dart';
import '../../features/notifications/providers/notifications_provider.dart';
import '../routing/app_routes.dart';
import '../../generated/l10n/app_localizations.dart';
import 'animated_sidebar.dart';
import 'sidebar_controller.dart';
import 'animated_hamburger_icon.dart';

/// Main layout with bottom navigation and drawer
class MainLayout extends ConsumerStatefulWidget {
  final Widget child;
  final String currentPath;

  const MainLayout({super.key, required this.child, required this.currentPath});

  @override
  ConsumerState<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends ConsumerState<MainLayout> {
  int _selectedIndex = 0;

  // Bottom navigation items
  final List<BottomNavigationBarItem> _bottomNavItems = const [
    BottomNavigationBarItem(icon: Icon(Icons.dashboard), label: 'Dashboard'),
    BottomNavigationBarItem(
      icon: Icon(Icons.calendar_today),
      label: 'Calendar',
    ),
    BottomNavigationBarItem(
      icon: Icon(Icons.qr_code_scanner, color: Colors.transparent),
      label: '',
    ), // Placeholder for QR scanner
    BottomNavigationBarItem(icon: Icon(Icons.people), label: 'Customers'),
    BottomNavigationBarItem(icon: Icon(Icons.message), label: 'Messages'),
  ];

  // Routes corresponding to bottom nav items (with QR scanner placeholder)
  final List<String> _routes = [
    '/dashboard',
    '/appointments',
    '', // QR scanner - handled separately
    '/customers',
    '/messages',
  ];

  @override
  void initState() {
    super.initState();
    _updateSelectedIndex();
  }

  @override
  void didUpdateWidget(MainLayout oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentPath != widget.currentPath) {
      _updateSelectedIndex();
      if (widget.currentPath == AppRoutes.dashboard) {
        ref.read(profileCompletionNotifierProvider.notifier).refresh();
      }
    }
  }

  void _updateSelectedIndex() {
    final index = _routes.indexOf(widget.currentPath);
    if (index != -1) {
      setState(() {
        _selectedIndex = index;
      });
    }
  }

  void _onBottomNavTap(int index) {
    // Handle QR scanner button (index 2)
    if (index == 2) {
      _openQRScanner();
      return;
    }

    final route = _routes[index];

    if (route.isNotEmpty && index != _selectedIndex) {
      context.go(route);
    }
  }

  void _openQRScanner() {
    // Navigate to QR scanner screen
    context.push('/qr-scanner');
  }

  @override
  Widget build(BuildContext context) {
    final sidebarState = ref.watch(sidebarControllerProvider);

    return Scaffold(
      extendBody: true,
      appBar: _buildAppBar(),
      body: Stack(
        children: [
          widget.child,
          AnimatedSidebar(
            isOpen: sidebarState.isOpen,
            onClose: () => ref.read(sidebarControllerProvider.notifier).close(),
            currentPath: widget.currentPath,
          ),
        ],
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: _buildQRScannerFAB(),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    final sidebarState = ref.watch(sidebarControllerProvider);

    return AppBar(
      title: _buildAppBarTitle(),
      leading: AnimatedHamburgerIcon(
        isOpen: sidebarState.isOpen,
        onPressed: () => ref.read(sidebarControllerProvider.notifier).toggle(),
      ),
      actions: _buildAppBarActions(),
    );
  }

  Widget _buildAppBarTitle() {
    if (widget.currentPath == '/messages') {
      // For messages screen, show title with unread count
      return Consumer(
        builder: (context, ref, child) {
          final l10n = AppLocalizations.of(context);
          final unreadCount = ref.watch(unreadMessagesCountNotifierProvider);
          return Row(
            children: [
              Text(l10n.messages),
              if (unreadCount > 0) ...[
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: context.colors.error,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    unreadCount.toString(),
                    style: context.textTheme.labelSmall?.copyWith(
                      color: context.colors.onError,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          );
        },
      );
    }
    return Text(_getAppBarTitle());
  }

  List<Widget> _buildAppBarActions() {
    if (widget.currentPath == '/messages') {
      return [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: () {
            // Refresh conversations and unread count
            ref
                .read(conversationsNotifierProvider.notifier)
                .refreshConversations();
            ref
                .read(unreadMessagesCountNotifierProvider.notifier)
                .refreshUnreadCount();
          },
          tooltip: 'Refresh conversations',
        ),
      ];
    }

    // Default actions for other screens
    return [
      Consumer(
        builder: (context, ref, child) {
          final unreadCount = ref.watch(unreadNotificationsCountProvider);
          return Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.notifications),
                onPressed: () => context.push(AppRoutes.notifications),
              ),
              if (unreadCount > 0)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 14,
                      minHeight: 14,
                    ),
                    child: Text(
                      unreadCount > 99 ? '99+' : unreadCount.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 8,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          );
        },
      ),
      IconButton(
        icon: const Icon(Icons.settings),
        onPressed: () => context.push('/settings'),
      ),
    ];
  }

  String _getAppBarTitle() {
    final l10n = AppLocalizations.of(context);
    switch (widget.currentPath) {
      case '/dashboard':
        return l10n.dashboard;
      case '/appointments':
        return l10n.calendar;
      case '/customers':
        return l10n.customers;
      case '/messages':
        return l10n.messages;
      default:
        return l10n.daltiProvider;
    }
  }

  Widget _buildBottomNavigationBar() {
    // Remove the QR scanner placeholder item for the actual bottom nav
    final navItems = [
      _bottomNavItems[0], // Dashboard
      _bottomNavItems[1], // Appointments
      _bottomNavItems[3], // Customers
      _bottomNavItems[4], // Messages
    ];

    return BottomAppBar(
      elevation: 0,
      shape: const CircularNotchedRectangle(),
      notchMargin: 6,
      clipBehavior: Clip.antiAlias,
      child: SizedBox(
        height: 60,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildNavItem(0, navItems[0]),
            _buildNavItem(1, navItems[1]),
            const SizedBox(width: 40), // Space for FAB
            _buildNavItem(3, navItems[2]), // Customers (index 3 in original)
            _buildNavItem(4, navItems[3]), // Messages (index 4 in original)
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem(int index, BottomNavigationBarItem item) {
    final isSelected = _selectedIndex == index;
    return Expanded(
      child: InkWell(
        onTap: () => _onBottomNavTap(index),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              item.icon is Icon ? (item.icon as Icon).icon : Icons.help,
              color:
                  isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 4),
            Text(
              item.label ?? '',
              style: TextStyle(
                fontSize: 12,
                color:
                    isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQRScannerFAB() {
    return FloatingActionButton(
      onPressed: _openQRScanner,
      backgroundColor: Theme.of(context).colorScheme.primary,
      shape: const CircleBorder(),
      elevation:
          0, // Very low elevation so SnackBars (elevation 6.0) appear above
      child: const Icon(Icons.qr_code_scanner, color: Colors.white, size: 28),
    );
  }
}

/// Helper function to determine if a route should use the main layout
bool shouldUseMainLayout(String path) {
  const mainLayoutRoutes = [
    '/dashboard',
    '/appointments',
    '/customers',
    '/analytics',
  ];

  return mainLayoutRoutes.contains(path);
}

/// Helper function to determine if a route should show back button
bool shouldShowBackButton(String path) {
  const noBackButtonRoutes = [
    '/dashboard',
    '/appointments',
    '/customers',
    '/analytics',
  ];

  return !noBackButtonRoutes.contains(path);
}

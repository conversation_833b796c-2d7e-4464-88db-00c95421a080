import 'package:flutter/material.dart';

/// Animated hamburger menu icon that transforms between hamburger and close states
class AnimatedHamburgerIcon extends StatefulWidget {
  final bool isOpen;
  final VoidCallback onPressed;
  final Color? color;
  final double size;

  const AnimatedHamburgerIcon({
    super.key,
    required this.isOpen,
    required this.onPressed,
    this.color,
    this.size = 24.0,
  });

  @override
  State<AnimatedHamburgerIcon> createState() => _AnimatedHamburgerIconState();
}

class _AnimatedHamburgerIconState extends State<AnimatedHamburgerIcon>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    if (widget.isOpen) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(AnimatedHamburgerIcon oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isOpen != oldWidget.isOpen) {
      if (widget.isOpen) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: widget.onPressed,
      icon: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return CustomPaint(
            size: Size(widget.size, widget.size),
            painter: _HamburgerIconPainter(
              progress: _animation.value,
              color:
                  widget.color ??
                  Theme.of(context).iconTheme.color ??
                  Colors.black,
            ),
          );
        },
      ),
    );
  }
}

class _HamburgerIconPainter extends CustomPainter {
  final double progress;
  final Color color;

  _HamburgerIconPainter({required this.progress, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..strokeWidth = 2.0
          ..strokeCap = StrokeCap.round;

    final width = size.width;
    final height = size.height;

    // Top line
    final topLineStart = Offset(0, height * 0.25);
    final topLineEnd = Offset(width, height * 0.25);
    final topLineRotatedStart = Offset(
      width * 0.2 + (width * 0.3 - width * 0.2) * progress,
      height * 0.25 + (height * 0.5 - height * 0.25) * progress,
    );
    final topLineRotatedEnd = Offset(
      width * 0.8 - (width * 0.8 - width * 0.7) * progress,
      height * 0.25 + (height * 0.5 - height * 0.25) * progress,
    );

    canvas.drawLine(
      Offset.lerp(topLineStart, topLineRotatedStart, progress)!,
      Offset.lerp(topLineEnd, topLineRotatedEnd, progress)!,
      paint,
    );

    // Middle line (fades out)
    if (progress < 1.0) {
      paint.color = color.withValues(alpha: 1.0 - progress);
      canvas.drawLine(
        Offset(0, height * 0.5),
        Offset(width, height * 0.5),
        paint,
      );
      paint.color = color; // Reset color
    }

    // Bottom line
    final bottomLineStart = Offset(0, height * 0.75);
    final bottomLineEnd = Offset(width, height * 0.75);
    final bottomLineRotatedStart = Offset(
      width * 0.2 + (width * 0.3 - width * 0.2) * progress,
      height * 0.75 - (height * 0.75 - height * 0.5) * progress,
    );
    final bottomLineRotatedEnd = Offset(
      width * 0.8 - (width * 0.8 - width * 0.7) * progress,
      height * 0.75 - (height * 0.75 - height * 0.5) * progress,
    );

    canvas.drawLine(
      Offset.lerp(bottomLineStart, bottomLineRotatedStart, progress)!,
      Offset.lerp(bottomLineEnd, bottomLineRotatedEnd, progress)!,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is _HamburgerIconPainter &&
        (oldDelegate.progress != progress || oldDelegate.color != color);
  }
}

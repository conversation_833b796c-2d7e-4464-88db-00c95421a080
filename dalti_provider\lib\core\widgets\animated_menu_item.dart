import 'package:flutter/material.dart';
import '../theme/theme_provider.dart';

/// Animated menu item widget for the sidebar
class AnimatedMenuItem extends StatefulWidget {
  final IconData icon;
  final String title;
  final bool isSelected;
  final VoidCallback onTap;
  final Color? iconColor;
  final Color? textColor;
  final Color? selectedColor;

  const AnimatedMenuItem({
    super.key,
    required this.icon,
    required this.title,
    required this.isSelected,
    required this.onTap,
    this.iconColor,
    this.textColor,
    this.selectedColor,
  });

  @override
  State<AnimatedMenuItem> createState() => _AnimatedMenuItemState();
}

class _AnimatedMenuItemState extends State<AnimatedMenuItem>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _selectionController;
  late Animation<double> _hoverAnimation;
  late Animation<double> _selectionAnimation;
  late Animation<double> _iconScaleAnimation;
  late Animation<Color?> _backgroundColorAnimation;

  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _selectionController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _hoverAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeOutCubic,
    ));

    _selectionAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _selectionController,
      curve: Curves.easeOutCubic,
    ));

    _iconScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeOutCubic,
    ));

    if (widget.isSelected) {
      _selectionController.forward();
    }
  }

  @override
  void didUpdateWidget(AnimatedMenuItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isSelected != oldWidget.isSelected) {
      if (widget.isSelected) {
        _selectionController.forward();
      } else {
        _selectionController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _selectionController.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });
    
    if (isHovered) {
      _hoverController.forward();
    } else {
      _hoverController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = widget.selectedColor ?? context.colors.primary;
    
    _backgroundColorAnimation = ColorTween(
      begin: Colors.transparent,
      end: primaryColor.withValues(alpha: 0.1),
    ).animate(_selectionAnimation);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      child: AnimatedBuilder(
        animation: Listenable.merge([_hoverAnimation, _selectionAnimation]),
        builder: (context, child) {
          return MouseRegion(
            onEnter: (_) => _onHover(true),
            onExit: (_) => _onHover(false),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: widget.onTap,
                borderRadius: BorderRadius.circular(12),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: _backgroundColorAnimation.value,
                    borderRadius: BorderRadius.circular(12),
                    border: widget.isSelected
                        ? Border.all(
                            color: primaryColor.withValues(alpha: 0.3),
                            width: 1,
                          )
                        : null,
                  ),
                  child: Row(
                    children: [
                      // Animated icon
                      AnimatedBuilder(
                        animation: _iconScaleAnimation,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _iconScaleAnimation.value,
                            child: Container(
                              width: 24,
                              height: 24,
                              decoration: widget.isSelected
                                  ? BoxDecoration(
                                      color: primaryColor.withValues(alpha: 0.2),
                                      borderRadius: BorderRadius.circular(6),
                                    )
                                  : null,
                              child: Icon(
                                widget.icon,
                                size: 20,
                                color: _getIconColor(primaryColor),
                              ),
                            ),
                          );
                        },
                      ),
                      
                      const SizedBox(width: 16),
                      
                      // Animated text
                      Expanded(
                        child: AnimatedDefaultTextStyle(
                          duration: const Duration(milliseconds: 200),
                          style: context.textTheme.bodyMedium?.copyWith(
                            color: _getTextColor(primaryColor),
                            fontWeight: widget.isSelected 
                                ? FontWeight.w600 
                                : FontWeight.normal,
                          ) ?? const TextStyle(),
                          child: Text(widget.title),
                        ),
                      ),
                      
                      // Selection indicator
                      if (widget.isSelected)
                        AnimatedBuilder(
                          animation: _selectionAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: _selectionAnimation.value,
                              child: Container(
                                width: 4,
                                height: 20,
                                decoration: BoxDecoration(
                                  color: primaryColor,
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              ),
                            );
                          },
                        ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Color _getIconColor(Color primaryColor) {
    if (widget.isSelected) {
      return primaryColor;
    }
    
    if (_isHovered) {
      return primaryColor.withValues(alpha: 0.8);
    }
    
    return widget.iconColor ?? 
           (context.isDarkMode ? Colors.white70 : Colors.black54);
  }

  Color _getTextColor(Color primaryColor) {
    if (widget.isSelected) {
      return primaryColor;
    }
    
    if (_isHovered) {
      return primaryColor.withValues(alpha: 0.8);
    }
    
    return widget.textColor ?? 
           (context.isDarkMode ? Colors.white : Colors.black87);
  }
}

import 'package:flutter/material.dart';
import '../theme/theme_provider.dart';

/// Section header widget for the sidebar
class <PERSON>barSectionHeader extends StatelessWidget {
  final String title;
  final Color? textColor;
  final EdgeInsetsGeometry? padding;

  const SidebarSectionHeader({
    super.key,
    required this.title,
    this.textColor,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Text(
        title.toUpperCase(),
        style: context.textTheme.labelSmall?.copyWith(
          color: textColor ?? 
                 (context.isDarkMode 
                     ? Colors.white.withValues(alpha: 0.5)
                     : Colors.black45),
          fontWeight: FontWeight.w600,
          letterSpacing: 1.2,
          fontSize: 11,
        ),
      ),
    );
  }
}
